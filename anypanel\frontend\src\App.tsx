import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import BasicLayout from './components/ProLayout';
import { RouteGuard } from './components/Access';
import Login from './pages/Login';
import AdminDashboard from './pages/admin/Dashboard';

// 全局加载组件
const GlobalLoading: React.FC = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
    }}
  >
    <Spin size="large" />
  </div>
);

// 占位页面组件
const PlaceholderPage: React.FC<{ title: string }> = ({ title }) => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '400px',
      fontSize: '18px',
      color: '#666',
    }}
  >
    {title} - 开发中...
  </div>
);

const App: React.FC = () => {
  return (
    <Suspense fallback={<GlobalLoading />}>
      <Routes>
        {/* 登录页面 */}
        <Route 
          path="/login" 
          element={
            <RouteGuard requireAuth={false}>
              <Login />
            </RouteGuard>
          } 
        />

        {/* 主应用布局 */}
        <Route
          path="/*"
          element={
            <RouteGuard>
              <BasicLayout />
            </RouteGuard>
          }
        >
          {/* 根路径重定向 */}
          <Route path="" element={<Navigate to="/admin/dashboard" replace />} />
          
          {/* 管理员路由 */}
          <Route
            path="admin/dashboard"
            element={
              <RouteGuard access="admin">
                <AdminDashboard />
              </RouteGuard>
            }
          />
          <Route
            path="admin/users"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="用户管理" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/nodes"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="节点管理" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/permission-groups"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="权限组管理" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/products"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="商品管理" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/orders"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="订单管理" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/traffic"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="流量分析" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/reports"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="报表中心" />
              </RouteGuard>
            }
          />
          <Route
            path="admin/settings"
            element={
              <RouteGuard access="admin">
                <PlaceholderPage title="系统设置" />
              </RouteGuard>
            }
          />

          {/* 用户路由 */}
          <Route
            path="user/dashboard"
            element={
              <RouteGuard access="user">
                <PlaceholderPage title="用户仪表板" />
              </RouteGuard>
            }
          />
          <Route
            path="user/store"
            element={
              <RouteGuard access="user">
                <PlaceholderPage title="商品商店" />
              </RouteGuard>
            }
          />
          <Route
            path="user/subscription"
            element={
              <RouteGuard access="user">
                <PlaceholderPage title="订阅中心" />
              </RouteGuard>
            }
          />
          <Route
            path="user/profile"
            element={
              <RouteGuard access="user">
                <PlaceholderPage title="个人设置" />
              </RouteGuard>
            }
          />

          {/* 404页面 */}
          <Route 
            path="*" 
            element={
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '400px',
                  fontSize: '18px',
                  color: '#666',
                }}
              >
                404 - 页面不存在
              </div>
            } 
          />
        </Route>
      </Routes>
    </Suspense>
  );
};

export default App;