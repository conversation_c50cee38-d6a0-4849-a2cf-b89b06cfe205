import axios from 'axios';

const API_BASE_URL = '/api/v1';

export interface SubscriptionStats {
  has_active_subscription: boolean;
  current_subscription?: {
    id: number;
    product_name: string;
    expired_at: string;
    days_left: number;
    is_expired: boolean;
    is_active: boolean;
  };
  subscription_history: Array<{
    id: number;
    product_name: string;
    status: string;
    started_at: string;
    expired_at: string;
  }>;
}

export interface SubscriptionUrls {
  base_url: string;
  token: string;
  urls: {
    clash: string;
    'sing-box': string;
    v2ray: string;
    shadowrocket: string;
    'quantumult-x': string;
    surge: string;
  };
}

export const SubscriptionService = {
  // 获取用户订阅信息
  async getUserSubscriptionInfo(): Promise<{ success: boolean; data: SubscriptionStats }> {
    const response = await axios.get(`${API_BASE_URL}/subscription/info`);
    return response.data;
  },

  // 获取用户订阅历史
  async getUserSubscriptionHistory(page: number = 1, pageSize: number = 20) {
    const response = await axios.get(`${API_BASE_URL}/subscription/history`, {
      params: { page, page_size: pageSize }
    });
    return response.data;
  },

  // 获取订阅链接
  async getSubscriptionUrls(): Promise<{ success: boolean; data: SubscriptionUrls }> {
    const response = await axios.get(`${API_BASE_URL}/subscription/urls`);
    return response.data;
  },

  // 重新生成订阅token
  async regenerateSubscriptionToken(): Promise<{ success: boolean; data: { token: string } }> {
    const response = await axios.put(`${API_BASE_URL}/subscription/regenerate-token`);
    return response.data;
  },

  // 下载配置文件
  async downloadClashConfig(): Promise<Blob> {
    const response = await axios.get(`${API_BASE_URL}/subscription/clash`, {
      responseType: 'blob'
    });
    return response.data;
  },

  async downloadSingBoxConfig(): Promise<Blob> {
    const response = await axios.get(`${API_BASE_URL}/subscription/sing-box`, {
      responseType: 'blob'
    });
    return response.data;
  },

  async downloadV2RayConfig(): Promise<Blob> {
    const response = await axios.get(`${API_BASE_URL}/subscription/v2ray`, {
      responseType: 'blob'
    });
    return response.data;
  },

  // 辅助函数：下载文件
  downloadFile(blob: Blob, filename: string) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // 复制到剪贴板
  async copyToClipboard(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      // 降级方案
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        const result = document.execCommand('copy');
        document.body.removeChild(textArea);
        return result;
      } catch (fallbackErr) {
        return false;
      }
    }
  }
};