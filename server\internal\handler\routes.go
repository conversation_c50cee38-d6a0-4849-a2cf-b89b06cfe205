// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.2

package handler

import (
	"github.com/gin-gonic/gin"
	adminAds "github.com/perfect-panel/server/internal/handler/admin/ads"
	adminAnnouncement "github.com/perfect-panel/server/internal/handler/admin/announcement"
	adminAuthMethod "github.com/perfect-panel/server/internal/handler/admin/authMethod"
	adminConsole "github.com/perfect-panel/server/internal/handler/admin/console"
	adminCoupon "github.com/perfect-panel/server/internal/handler/admin/coupon"
	adminDocument "github.com/perfect-panel/server/internal/handler/admin/document"
	adminLog "github.com/perfect-panel/server/internal/handler/admin/log"
	adminOrder "github.com/perfect-panel/server/internal/handler/admin/order"
	adminPayment "github.com/perfect-panel/server/internal/handler/admin/payment"
	adminServer "github.com/perfect-panel/server/internal/handler/admin/server"
	adminSubscribe "github.com/perfect-panel/server/internal/handler/admin/subscribe"
	adminSystem "github.com/perfect-panel/server/internal/handler/admin/system"
	adminTicket "github.com/perfect-panel/server/internal/handler/admin/ticket"
	adminTool "github.com/perfect-panel/server/internal/handler/admin/tool"
	adminUser "github.com/perfect-panel/server/internal/handler/admin/user"
	appAnnouncement "github.com/perfect-panel/server/internal/handler/app/announcement"
	appAuth "github.com/perfect-panel/server/internal/handler/app/auth"
	appDocument "github.com/perfect-panel/server/internal/handler/app/document"
	appNode "github.com/perfect-panel/server/internal/handler/app/node"
	appOrder "github.com/perfect-panel/server/internal/handler/app/order"
	appPayment "github.com/perfect-panel/server/internal/handler/app/payment"
	appSubscribe "github.com/perfect-panel/server/internal/handler/app/subscribe"
	appUser "github.com/perfect-panel/server/internal/handler/app/user"
	appWs "github.com/perfect-panel/server/internal/handler/app/ws"
	auth "github.com/perfect-panel/server/internal/handler/auth"
	authOauth "github.com/perfect-panel/server/internal/handler/auth/oauth"
	common "github.com/perfect-panel/server/internal/handler/common"
	publicAnnouncement "github.com/perfect-panel/server/internal/handler/public/announcement"
	publicDocument "github.com/perfect-panel/server/internal/handler/public/document"
	publicOrder "github.com/perfect-panel/server/internal/handler/public/order"
	publicPayment "github.com/perfect-panel/server/internal/handler/public/payment"
	publicPortal "github.com/perfect-panel/server/internal/handler/public/portal"
	publicSubscribe "github.com/perfect-panel/server/internal/handler/public/subscribe"
	publicTicket "github.com/perfect-panel/server/internal/handler/public/ticket"
	publicUser "github.com/perfect-panel/server/internal/handler/public/user"
	server "github.com/perfect-panel/server/internal/handler/server"
	"github.com/perfect-panel/server/internal/middleware"
	"github.com/perfect-panel/server/internal/svc"
)

func RegisterHandlers(router *gin.Engine, serverCtx *svc.ServiceContext) {
	adminAdsGroupRouter := router.Group("/v1/admin/ads")
	adminAdsGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create Ads
		adminAdsGroupRouter.POST("/", adminAds.CreateAdsHandler(serverCtx))

		// Update Ads
		adminAdsGroupRouter.PUT("/", adminAds.UpdateAdsHandler(serverCtx))

		// Delete Ads
		adminAdsGroupRouter.DELETE("/", adminAds.DeleteAdsHandler(serverCtx))

		// Get Ads Detail
		adminAdsGroupRouter.GET("/detail", adminAds.GetAdsDetailHandler(serverCtx))

		// Get Ads List
		adminAdsGroupRouter.GET("/list", adminAds.GetAdsListHandler(serverCtx))
	}

	adminAnnouncementGroupRouter := router.Group("/v1/admin/announcement")
	adminAnnouncementGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create announcement
		adminAnnouncementGroupRouter.POST("/", adminAnnouncement.CreateAnnouncementHandler(serverCtx))

		// Update announcement
		adminAnnouncementGroupRouter.PUT("/", adminAnnouncement.UpdateAnnouncementHandler(serverCtx))

		// Delete announcement
		adminAnnouncementGroupRouter.DELETE("/", adminAnnouncement.DeleteAnnouncementHandler(serverCtx))

		// Get announcement
		adminAnnouncementGroupRouter.GET("/detail", adminAnnouncement.GetAnnouncementHandler(serverCtx))

		// Get announcement list
		adminAnnouncementGroupRouter.GET("/list", adminAnnouncement.GetAnnouncementListHandler(serverCtx))
	}

	adminAuthMethodGroupRouter := router.Group("/v1/admin/auth-method")
	adminAuthMethodGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get auth method config
		adminAuthMethodGroupRouter.GET("/config", adminAuthMethod.GetAuthMethodConfigHandler(serverCtx))

		// Update auth method config
		adminAuthMethodGroupRouter.PUT("/config", adminAuthMethod.UpdateAuthMethodConfigHandler(serverCtx))

		// Get email support platform
		adminAuthMethodGroupRouter.GET("/email_platform", adminAuthMethod.GetEmailPlatformHandler(serverCtx))

		// Get auth method list
		adminAuthMethodGroupRouter.GET("/list", adminAuthMethod.GetAuthMethodListHandler(serverCtx))

		// Get sms support platform
		adminAuthMethodGroupRouter.GET("/sms_platform", adminAuthMethod.GetSmsPlatformHandler(serverCtx))

		// Test email send
		adminAuthMethodGroupRouter.POST("/test_email_send", adminAuthMethod.TestEmailSendHandler(serverCtx))

		// Test sms send
		adminAuthMethodGroupRouter.POST("/test_sms_send", adminAuthMethod.TestSmsSendHandler(serverCtx))
	}

	adminConsoleGroupRouter := router.Group("/v1/admin/console")
	adminConsoleGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Query revenue statistics
		adminConsoleGroupRouter.GET("/revenue", adminConsole.QueryRevenueStatisticsHandler(serverCtx))

		// Query server total data
		adminConsoleGroupRouter.GET("/server", adminConsole.QueryServerTotalDataHandler(serverCtx))

		// Query ticket wait reply
		adminConsoleGroupRouter.GET("/ticket", adminConsole.QueryTicketWaitReplyHandler(serverCtx))

		// Query user statistics
		adminConsoleGroupRouter.GET("/user", adminConsole.QueryUserStatisticsHandler(serverCtx))
	}

	adminCouponGroupRouter := router.Group("/v1/admin/coupon")
	adminCouponGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create coupon
		adminCouponGroupRouter.POST("/", adminCoupon.CreateCouponHandler(serverCtx))

		// Update coupon
		adminCouponGroupRouter.PUT("/", adminCoupon.UpdateCouponHandler(serverCtx))

		// Delete coupon
		adminCouponGroupRouter.DELETE("/", adminCoupon.DeleteCouponHandler(serverCtx))

		// Batch delete coupon
		adminCouponGroupRouter.DELETE("/batch", adminCoupon.BatchDeleteCouponHandler(serverCtx))

		// Get coupon list
		adminCouponGroupRouter.GET("/list", adminCoupon.GetCouponListHandler(serverCtx))
	}

	adminDocumentGroupRouter := router.Group("/v1/admin/document")
	adminDocumentGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create document
		adminDocumentGroupRouter.POST("/", adminDocument.CreateDocumentHandler(serverCtx))

		// Update document
		adminDocumentGroupRouter.PUT("/", adminDocument.UpdateDocumentHandler(serverCtx))

		// Delete document
		adminDocumentGroupRouter.DELETE("/", adminDocument.DeleteDocumentHandler(serverCtx))

		// Batch delete document
		adminDocumentGroupRouter.DELETE("/batch", adminDocument.BatchDeleteDocumentHandler(serverCtx))

		// Get document detail
		adminDocumentGroupRouter.GET("/detail", adminDocument.GetDocumentDetailHandler(serverCtx))

		// Get document list
		adminDocumentGroupRouter.GET("/list", adminDocument.GetDocumentListHandler(serverCtx))
	}

	adminLogGroupRouter := router.Group("/v1/admin/log")
	adminLogGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get message log list
		adminLogGroupRouter.GET("/message/list", adminLog.GetMessageLogListHandler(serverCtx))
	}

	adminOrderGroupRouter := router.Group("/v1/admin/order")
	adminOrderGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create order
		adminOrderGroupRouter.POST("/", adminOrder.CreateOrderHandler(serverCtx))

		// Get order list
		adminOrderGroupRouter.GET("/list", adminOrder.GetOrderListHandler(serverCtx))

		// Update order status
		adminOrderGroupRouter.PUT("/status", adminOrder.UpdateOrderStatusHandler(serverCtx))
	}

	adminPaymentGroupRouter := router.Group("/v1/admin/payment")
	adminPaymentGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create Payment Method
		adminPaymentGroupRouter.POST("/", adminPayment.CreatePaymentMethodHandler(serverCtx))

		// Update Payment Method
		adminPaymentGroupRouter.PUT("/", adminPayment.UpdatePaymentMethodHandler(serverCtx))

		// Delete Payment Method
		adminPaymentGroupRouter.DELETE("/", adminPayment.DeletePaymentMethodHandler(serverCtx))

		// Get Payment Method List
		adminPaymentGroupRouter.GET("/list", adminPayment.GetPaymentMethodListHandler(serverCtx))

		// Get supported payment platform
		adminPaymentGroupRouter.GET("/platform", adminPayment.GetPaymentPlatformHandler(serverCtx))
	}

	adminServerGroupRouter := router.Group("/v1/admin/server")
	adminServerGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Update node
		adminServerGroupRouter.PUT("/", adminServer.UpdateNodeHandler(serverCtx))

		// Create node
		adminServerGroupRouter.POST("/", adminServer.CreateNodeHandler(serverCtx))

		// Delete node
		adminServerGroupRouter.DELETE("/", adminServer.DeleteNodeHandler(serverCtx))

		// Batch delete node
		adminServerGroupRouter.DELETE("/batch", adminServer.BatchDeleteNodeHandler(serverCtx))

		// Get node detail
		adminServerGroupRouter.GET("/detail", adminServer.GetNodeDetailHandler(serverCtx))

		// Create node group
		adminServerGroupRouter.POST("/group", adminServer.CreateNodeGroupHandler(serverCtx))

		// Update node group
		adminServerGroupRouter.PUT("/group", adminServer.UpdateNodeGroupHandler(serverCtx))

		// Delete node group
		adminServerGroupRouter.DELETE("/group", adminServer.DeleteNodeGroupHandler(serverCtx))

		// Batch delete node group
		adminServerGroupRouter.DELETE("/group/batch", adminServer.BatchDeleteNodeGroupHandler(serverCtx))

		// Get node group list
		adminServerGroupRouter.GET("/group/list", adminServer.GetNodeGroupListHandler(serverCtx))

		// Get node list
		adminServerGroupRouter.GET("/list", adminServer.GetNodeListHandler(serverCtx))

		// Create rule group
		adminServerGroupRouter.POST("/rule_group", adminServer.CreateRuleGroupHandler(serverCtx))

		// Update rule group
		adminServerGroupRouter.PUT("/rule_group", adminServer.UpdateRuleGroupHandler(serverCtx))

		// Delete rule group
		adminServerGroupRouter.DELETE("/rule_group", adminServer.DeleteRuleGroupHandler(serverCtx))

		// Get rule group list
		adminServerGroupRouter.GET("/rule_group_list", adminServer.GetRuleGroupListHandler(serverCtx))

		// Node sort
		adminServerGroupRouter.POST("/sort", adminServer.NodeSortHandler(serverCtx))

		// Get node tag list
		adminServerGroupRouter.GET("/tag/list", adminServer.GetNodeTagListHandler(serverCtx))
	}

	adminSubscribeGroupRouter := router.Group("/v1/admin/subscribe")
	adminSubscribeGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Create subscribe
		adminSubscribeGroupRouter.POST("/", adminSubscribe.CreateSubscribeHandler(serverCtx))

		// Update subscribe
		adminSubscribeGroupRouter.PUT("/", adminSubscribe.UpdateSubscribeHandler(serverCtx))

		// Delete subscribe
		adminSubscribeGroupRouter.DELETE("/", adminSubscribe.DeleteSubscribeHandler(serverCtx))

		// Batch delete subscribe
		adminSubscribeGroupRouter.DELETE("/batch", adminSubscribe.BatchDeleteSubscribeHandler(serverCtx))

		// Get subscribe details
		adminSubscribeGroupRouter.GET("/details", adminSubscribe.GetSubscribeDetailsHandler(serverCtx))

		// Create subscribe group
		adminSubscribeGroupRouter.POST("/group", adminSubscribe.CreateSubscribeGroupHandler(serverCtx))

		// Update subscribe group
		adminSubscribeGroupRouter.PUT("/group", adminSubscribe.UpdateSubscribeGroupHandler(serverCtx))

		// Delete subscribe group
		adminSubscribeGroupRouter.DELETE("/group", adminSubscribe.DeleteSubscribeGroupHandler(serverCtx))

		// Batch delete subscribe group
		adminSubscribeGroupRouter.DELETE("/group/batch", adminSubscribe.BatchDeleteSubscribeGroupHandler(serverCtx))

		// Get subscribe group list
		adminSubscribeGroupRouter.GET("/group/list", adminSubscribe.GetSubscribeGroupListHandler(serverCtx))

		// Get subscribe list
		adminSubscribeGroupRouter.GET("/list", adminSubscribe.GetSubscribeListHandler(serverCtx))

		// Subscribe sort
		adminSubscribeGroupRouter.POST("/sort", adminSubscribe.SubscribeSortHandler(serverCtx))
	}

	adminSystemGroupRouter := router.Group("/v1/admin/system")
	adminSystemGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get application
		adminSystemGroupRouter.GET("/application", adminSystem.GetApplicationHandler(serverCtx))

		// Update application
		adminSystemGroupRouter.PUT("/application", adminSystem.UpdateApplicationHandler(serverCtx))

		// Create application
		adminSystemGroupRouter.POST("/application", adminSystem.CreateApplicationHandler(serverCtx))

		// Delete application
		adminSystemGroupRouter.DELETE("/application", adminSystem.DeleteApplicationHandler(serverCtx))

		// update application config
		adminSystemGroupRouter.PUT("/application_config", adminSystem.UpdateApplicationConfigHandler(serverCtx))

		// get application config
		adminSystemGroupRouter.GET("/application_config", adminSystem.GetApplicationConfigHandler(serverCtx))

		// Update application version
		adminSystemGroupRouter.PUT("/application_version", adminSystem.UpdateApplicationVersionHandler(serverCtx))

		// Create application version
		adminSystemGroupRouter.POST("/application_version", adminSystem.CreateApplicationVersionHandler(serverCtx))

		// Delete application
		adminSystemGroupRouter.DELETE("/application_version", adminSystem.DeleteApplicationVersionHandler(serverCtx))

		// Get Currency Config
		adminSystemGroupRouter.GET("/currency_config", adminSystem.GetCurrencyConfigHandler(serverCtx))

		// Update Currency Config
		adminSystemGroupRouter.PUT("/currency_config", adminSystem.UpdateCurrencyConfigHandler(serverCtx))

		// Get Node Multiplier
		adminSystemGroupRouter.GET("/get_node_multiplier", adminSystem.GetNodeMultiplierHandler(serverCtx))

		// Get invite config
		adminSystemGroupRouter.GET("/invite_config", adminSystem.GetInviteConfigHandler(serverCtx))

		// Update invite config
		adminSystemGroupRouter.PUT("/invite_config", adminSystem.UpdateInviteConfigHandler(serverCtx))

		// Get node config
		adminSystemGroupRouter.GET("/node_config", adminSystem.GetNodeConfigHandler(serverCtx))

		// Update node config
		adminSystemGroupRouter.PUT("/node_config", adminSystem.UpdateNodeConfigHandler(serverCtx))

		// get Privacy Policy Config
		adminSystemGroupRouter.GET("/privacy", adminSystem.GetPrivacyPolicyConfigHandler(serverCtx))

		// Update Privacy Policy Config
		adminSystemGroupRouter.PUT("/privacy", adminSystem.UpdatePrivacyPolicyConfigHandler(serverCtx))

		// Get register config
		adminSystemGroupRouter.GET("/register_config", adminSystem.GetRegisterConfigHandler(serverCtx))

		// Update register config
		adminSystemGroupRouter.PUT("/register_config", adminSystem.UpdateRegisterConfigHandler(serverCtx))

		// Set Node Multiplier
		adminSystemGroupRouter.POST("/set_node_multiplier", adminSystem.SetNodeMultiplierHandler(serverCtx))

		// setting telegram bot
		adminSystemGroupRouter.POST("/setting_telegram_bot", adminSystem.SettingTelegramBotHandler(serverCtx))

		// Get site config
		adminSystemGroupRouter.GET("/site_config", adminSystem.GetSiteConfigHandler(serverCtx))

		// Update site config
		adminSystemGroupRouter.PUT("/site_config", adminSystem.UpdateSiteConfigHandler(serverCtx))

		// Get subscribe config
		adminSystemGroupRouter.GET("/subscribe_config", adminSystem.GetSubscribeConfigHandler(serverCtx))

		// Update subscribe config
		adminSystemGroupRouter.PUT("/subscribe_config", adminSystem.UpdateSubscribeConfigHandler(serverCtx))

		// Get subscribe type
		adminSystemGroupRouter.GET("/subscribe_type", adminSystem.GetSubscribeTypeHandler(serverCtx))

		// Get Team of Service Config
		adminSystemGroupRouter.GET("/tos_config", adminSystem.GetTosConfigHandler(serverCtx))

		// Update Team of Service Config
		adminSystemGroupRouter.PUT("/tos_config", adminSystem.UpdateTosConfigHandler(serverCtx))

		// Get Verify Code Config
		adminSystemGroupRouter.GET("/verify_code_config", adminSystem.GetVerifyCodeConfigHandler(serverCtx))

		// Update Verify Code Config
		adminSystemGroupRouter.PUT("/verify_code_config", adminSystem.UpdateVerifyCodeConfigHandler(serverCtx))

		// Get verify config
		adminSystemGroupRouter.GET("/verify_config", adminSystem.GetVerifyConfigHandler(serverCtx))

		// Update verify config
		adminSystemGroupRouter.PUT("/verify_config", adminSystem.UpdateVerifyConfigHandler(serverCtx))
	}

	adminTicketGroupRouter := router.Group("/v1/admin/ticket")
	adminTicketGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Update ticket status
		adminTicketGroupRouter.PUT("/", adminTicket.UpdateTicketStatusHandler(serverCtx))

		// Get ticket detail
		adminTicketGroupRouter.GET("/detail", adminTicket.GetTicketHandler(serverCtx))

		// Create ticket follow
		adminTicketGroupRouter.POST("/follow", adminTicket.CreateTicketFollowHandler(serverCtx))

		// Get ticket list
		adminTicketGroupRouter.GET("/list", adminTicket.GetTicketListHandler(serverCtx))
	}

	adminToolGroupRouter := router.Group("/v1/admin/tool")
	adminToolGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get System Log
		adminToolGroupRouter.GET("/log", adminTool.GetSystemLogHandler(serverCtx))

		// Restart System
		adminToolGroupRouter.GET("/restart", adminTool.RestartSystemHandler(serverCtx))
	}

	adminUserGroupRouter := router.Group("/v1/admin/user")
	adminUserGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Delete user
		adminUserGroupRouter.DELETE("/", adminUser.DeleteUserHandler(serverCtx))

		// Create user
		adminUserGroupRouter.POST("/", adminUser.CreateUserHandler(serverCtx))

		// Create user auth method
		adminUserGroupRouter.POST("/auth_method", adminUser.CreateUserAuthMethodHandler(serverCtx))

		// Delete user auth method
		adminUserGroupRouter.DELETE("/auth_method", adminUser.DeleteUserAuthMethodHandler(serverCtx))

		// Update user auth method
		adminUserGroupRouter.PUT("/auth_method", adminUser.UpdateUserAuthMethodHandler(serverCtx))

		// Get user auth method
		adminUserGroupRouter.GET("/auth_method", adminUser.GetUserAuthMethodHandler(serverCtx))

		// Update user basic info
		adminUserGroupRouter.PUT("/basic", adminUser.UpdateUserBasicInfoHandler(serverCtx))

		// Batch delete user
		adminUserGroupRouter.DELETE("/batch", adminUser.BatchDeleteUserHandler(serverCtx))

		// Current user
		adminUserGroupRouter.GET("/current", adminUser.CurrentUserHandler(serverCtx))

		// Get user detail
		adminUserGroupRouter.GET("/detail", adminUser.GetUserDetailHandler(serverCtx))

		// User device
		adminUserGroupRouter.PUT("/device", adminUser.UpdateUserDeviceHandler(serverCtx))

		// Delete user device
		adminUserGroupRouter.DELETE("/device", adminUser.DeleteUserDeviceHandler(serverCtx))

		// kick offline user device
		adminUserGroupRouter.PUT("/device/kick_offline", adminUser.KickOfflineByUserDeviceHandler(serverCtx))

		// Get user list
		adminUserGroupRouter.GET("/list", adminUser.GetUserListHandler(serverCtx))

		// Get user login logs
		adminUserGroupRouter.GET("/login/logs", adminUser.GetUserLoginLogsHandler(serverCtx))

		// Update user notify setting
		adminUserGroupRouter.PUT("/notify", adminUser.UpdateUserNotifySettingHandler(serverCtx))

		// Get user subcribe
		adminUserGroupRouter.GET("/subscribe", adminUser.GetUserSubscribeHandler(serverCtx))

		// Create user subcribe
		adminUserGroupRouter.POST("/subscribe", adminUser.CreateUserSubscribeHandler(serverCtx))

		// Update user subcribe
		adminUserGroupRouter.PUT("/subscribe", adminUser.UpdateUserSubscribeHandler(serverCtx))

		// Delete user subcribe
		adminUserGroupRouter.DELETE("/subscribe", adminUser.DeleteUserSubscribeHandler(serverCtx))

		// Get user subcribe by id
		adminUserGroupRouter.GET("/subscribe/detail", adminUser.GetUserSubscribeByIdHandler(serverCtx))

		// Get user subcribe devices
		adminUserGroupRouter.GET("/subscribe/device", adminUser.GetUserSubscribeDevicesHandler(serverCtx))

		// Get user subcribe logs
		adminUserGroupRouter.GET("/subscribe/logs", adminUser.GetUserSubscribeLogsHandler(serverCtx))

		// Get user subcribe traffic logs
		adminUserGroupRouter.GET("/subscribe/traffic_logs", adminUser.GetUserSubscribeTrafficLogsHandler(serverCtx))
	}

	appAnnouncementGroupRouter := router.Group("/v1/app/announcement")
	appAnnouncementGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Query announcement
		appAnnouncementGroupRouter.GET("/list", appAnnouncement.QueryAnnouncementHandler(serverCtx))
	}

	appAuthGroupRouter := router.Group("/v1/app/auth")
	appAuthGroupRouter.Use(middleware.AppMiddleware(serverCtx))

	{
		// Check Account
		appAuthGroupRouter.POST("/check", appAuth.CheckHandler(serverCtx))

		// GetAppConfig
		appAuthGroupRouter.POST("/config", appAuth.GetAppConfigHandler(serverCtx))

		// Login
		appAuthGroupRouter.POST("/login", appAuth.LoginHandler(serverCtx))

		// Register
		appAuthGroupRouter.POST("/register", appAuth.RegisterHandler(serverCtx))

		// Reset Password
		appAuthGroupRouter.POST("/reset_password", appAuth.ResetPasswordHandler(serverCtx))
	}

	appDocumentGroupRouter := router.Group("/v1/app/document")
	appDocumentGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Get document detail
		appDocumentGroupRouter.GET("/detail", appDocument.QueryDocumentDetailHandler(serverCtx))

		// Get document list
		appDocumentGroupRouter.GET("/list", appDocument.QueryDocumentListHandler(serverCtx))
	}

	appNodeGroupRouter := router.Group("/v1/app/node")
	appNodeGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Get Node list
		appNodeGroupRouter.GET("/list", appNode.GetNodeListHandler(serverCtx))

		// Get rule group list
		appNodeGroupRouter.GET("/rule_group_list", appNode.GetRuleGroupListHandler(serverCtx))
	}

	appOrderGroupRouter := router.Group("/v1/app/order")
	appOrderGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Checkout order
		appOrderGroupRouter.POST("/checkout", appOrder.CheckoutOrderHandler(serverCtx))

		// Close order
		appOrderGroupRouter.POST("/close", appOrder.CloseOrderHandler(serverCtx))

		// Get order
		appOrderGroupRouter.GET("/detail", appOrder.QueryOrderDetailHandler(serverCtx))

		// Get order list
		appOrderGroupRouter.GET("/list", appOrder.QueryOrderListHandler(serverCtx))

		// Pre create order
		appOrderGroupRouter.POST("/pre", appOrder.PreCreateOrderHandler(serverCtx))

		// purchase Subscription
		appOrderGroupRouter.POST("/purchase", appOrder.PurchaseHandler(serverCtx))

		// Recharge
		appOrderGroupRouter.POST("/recharge", appOrder.RechargeHandler(serverCtx))

		// Renewal Subscription
		appOrderGroupRouter.POST("/renewal", appOrder.RenewalHandler(serverCtx))

		// Reset traffic
		appOrderGroupRouter.POST("/reset", appOrder.ResetTrafficHandler(serverCtx))
	}

	appPaymentGroupRouter := router.Group("/v1/app/payment")
	appPaymentGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Get available payment methods
		appPaymentGroupRouter.GET("/methods", appPayment.GetAvailablePaymentMethodsHandler(serverCtx))
	}

	appSubscribeGroupRouter := router.Group("/v1/app/subscribe")
	appSubscribeGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Get application config
		appSubscribeGroupRouter.GET("/application/config", appSubscribe.QueryApplicationConfigHandler(serverCtx))

		// Get subscribe group list
		appSubscribeGroupRouter.GET("/group/list", appSubscribe.QuerySubscribeGroupListHandler(serverCtx))

		// Get subscribe list
		appSubscribeGroupRouter.GET("/list", appSubscribe.QuerySubscribeListHandler(serverCtx))

		// Reset user subscription period
		appSubscribeGroupRouter.POST("/reset/period", appSubscribe.ResetUserSubscribePeriodHandler(serverCtx))

		// Get  Already subscribed to package
		appSubscribeGroupRouter.GET("/user/already_subscribe", appSubscribe.QueryUserAlreadySubscribeHandler(serverCtx))

		// Get Available subscriptions for users
		appSubscribeGroupRouter.GET("/user/available_subscribe", appSubscribe.QueryUserAvailableUserSubscribeHandler(serverCtx))
	}

	appUserGroupRouter := router.Group("/v1/app/user")
	appUserGroupRouter.Use(middleware.AppMiddleware(serverCtx), middleware.AuthMiddleware(serverCtx))

	{
		// Delete Account
		appUserGroupRouter.DELETE("/account", appUser.DeleteAccountHandler(serverCtx))

		// Query User Affiliate Count
		appUserGroupRouter.GET("/affiliate/count", appUser.QueryUserAffiliateHandler(serverCtx))

		// Query User Affiliate List
		appUserGroupRouter.GET("/affiliate/list", appUser.QueryUserAffiliateListHandler(serverCtx))

		// query user info
		appUserGroupRouter.GET("/info", appUser.QueryUserInfoHandler(serverCtx))

		// Get user online time total
		appUserGroupRouter.GET("/online_time/statistics", appUser.GetUserOnlineTimeStatisticsHandler(serverCtx))

		// Update Password
		appUserGroupRouter.PUT("/password", appUser.UpdatePasswordHandler(serverCtx))

		// Get user subcribe traffic logs
		appUserGroupRouter.GET("/subscribe/traffic_logs", appUser.GetUserSubscribeTrafficLogsHandler(serverCtx))
	}

	appWsGroupRouter := router.Group("/v1/app/ws")
	appWsGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// App heartbeat
		appWsGroupRouter.GET("/:userid/:identifier", appWs.AppWsHandler(serverCtx))
	}

	authGroupRouter := router.Group("/v1/auth")

	{
		// Check user is exist
		authGroupRouter.GET("/check", auth.CheckUserHandler(serverCtx))

		// Check user telephone is exist
		authGroupRouter.GET("/check/telephone", auth.CheckUserTelephoneHandler(serverCtx))

		// User login
		authGroupRouter.POST("/login", auth.UserLoginHandler(serverCtx))

		// User Telephone login
		authGroupRouter.POST("/login/telephone", auth.TelephoneLoginHandler(serverCtx))

		// User register
		authGroupRouter.POST("/register", auth.UserRegisterHandler(serverCtx))

		// User Telephone register
		authGroupRouter.POST("/register/telephone", auth.TelephoneUserRegisterHandler(serverCtx))

		// Reset password
		authGroupRouter.POST("/reset", auth.ResetPasswordHandler(serverCtx))

		// Reset password
		authGroupRouter.POST("/reset/telephone", auth.TelephoneResetPasswordHandler(serverCtx))
	}

	authOauthGroupRouter := router.Group("/v1/auth/oauth")

	{
		// Apple Login Callback
		authOauthGroupRouter.POST("/callback/apple", authOauth.AppleLoginCallbackHandler(serverCtx))

		// OAuth login
		authOauthGroupRouter.POST("/login", authOauth.OAuthLoginHandler(serverCtx))

		// OAuth login get token
		authOauthGroupRouter.POST("/login/token", authOauth.OAuthLoginGetTokenHandler(serverCtx))
	}

	commonGroupRouter := router.Group("/v1/common")

	{
		// Get Ads
		commonGroupRouter.GET("/ads", common.GetAdsHandler(serverCtx))

		// Get Tos Content
		commonGroupRouter.GET("/application", common.GetApplicationHandler(serverCtx))

		// Check verification code
		commonGroupRouter.POST("/check_verification_code", common.CheckVerificationCodeHandler(serverCtx))

		// Get verification code
		commonGroupRouter.POST("/send_code", common.SendEmailCodeHandler(serverCtx))

		// Get sms verification code
		commonGroupRouter.POST("/send_sms_code", common.SendSmsCodeHandler(serverCtx))

		// Get global config
		commonGroupRouter.GET("/site/config", common.GetGlobalConfigHandler(serverCtx))

		// Get Privacy Policy
		commonGroupRouter.GET("/site/privacy", common.GetPrivacyPolicyHandler(serverCtx))

		// Get stat
		commonGroupRouter.GET("/site/stat", common.GetStatHandler(serverCtx))

		// Get Tos Content
		commonGroupRouter.GET("/site/tos", common.GetTosHandler(serverCtx))
	}

	publicAnnouncementGroupRouter := router.Group("/v1/public/announcement")
	publicAnnouncementGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Query announcement
		publicAnnouncementGroupRouter.GET("/list", publicAnnouncement.QueryAnnouncementHandler(serverCtx))
	}

	publicDocumentGroupRouter := router.Group("/v1/public/document")
	publicDocumentGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get document detail
		publicDocumentGroupRouter.GET("/detail", publicDocument.QueryDocumentDetailHandler(serverCtx))

		// Get document list
		publicDocumentGroupRouter.GET("/list", publicDocument.QueryDocumentListHandler(serverCtx))
	}

	publicOrderGroupRouter := router.Group("/v1/public/order")
	publicOrderGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Close order
		publicOrderGroupRouter.POST("/close", publicOrder.CloseOrderHandler(serverCtx))

		// Get order
		publicOrderGroupRouter.GET("/detail", publicOrder.QueryOrderDetailHandler(serverCtx))

		// Get order list
		publicOrderGroupRouter.GET("/list", publicOrder.QueryOrderListHandler(serverCtx))

		// Pre create order
		publicOrderGroupRouter.POST("/pre", publicOrder.PreCreateOrderHandler(serverCtx))

		// purchase Subscription
		publicOrderGroupRouter.POST("/purchase", publicOrder.PurchaseHandler(serverCtx))

		// Recharge
		publicOrderGroupRouter.POST("/recharge", publicOrder.RechargeHandler(serverCtx))

		// Renewal Subscription
		publicOrderGroupRouter.POST("/renewal", publicOrder.RenewalHandler(serverCtx))

		// Reset traffic
		publicOrderGroupRouter.POST("/reset", publicOrder.ResetTrafficHandler(serverCtx))
	}

	publicPaymentGroupRouter := router.Group("/v1/public/payment")
	publicPaymentGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get available payment methods
		publicPaymentGroupRouter.GET("/methods", publicPayment.GetAvailablePaymentMethodsHandler(serverCtx))
	}

	publicPortalGroupRouter := router.Group("/v1/public/portal")

	{
		// Purchase Checkout
		publicPortalGroupRouter.POST("/order/checkout", publicPortal.PurchaseCheckoutHandler(serverCtx))

		// Query Purchase Order
		publicPortalGroupRouter.GET("/order/status", publicPortal.QueryPurchaseOrderHandler(serverCtx))

		// Get available payment methods
		publicPortalGroupRouter.GET("/payment-method", publicPortal.GetAvailablePaymentMethodsHandler(serverCtx))

		// Pre Purchase Order
		publicPortalGroupRouter.POST("/pre", publicPortal.PrePurchaseOrderHandler(serverCtx))

		// Purchase subscription
		publicPortalGroupRouter.POST("/purchase", publicPortal.PurchaseHandler(serverCtx))

		// Get Subscription
		publicPortalGroupRouter.GET("/subscribe", publicPortal.GetSubscriptionHandler(serverCtx))
	}

	publicSubscribeGroupRouter := router.Group("/v1/public/subscribe")
	publicSubscribeGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Get application config
		publicSubscribeGroupRouter.GET("/application/config", publicSubscribe.QueryApplicationConfigHandler(serverCtx))

		// Get subscribe group list
		publicSubscribeGroupRouter.GET("/group/list", publicSubscribe.QuerySubscribeGroupListHandler(serverCtx))

		// Get subscribe list
		publicSubscribeGroupRouter.GET("/list", publicSubscribe.QuerySubscribeListHandler(serverCtx))
	}

	publicTicketGroupRouter := router.Group("/v1/public/ticket")
	publicTicketGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Update ticket status
		publicTicketGroupRouter.PUT("/", publicTicket.UpdateUserTicketStatusHandler(serverCtx))

		// Create ticket
		publicTicketGroupRouter.POST("/", publicTicket.CreateUserTicketHandler(serverCtx))

		// Get ticket detail
		publicTicketGroupRouter.GET("/detail", publicTicket.GetUserTicketDetailsHandler(serverCtx))

		// Create ticket follow
		publicTicketGroupRouter.POST("/follow", publicTicket.CreateUserTicketFollowHandler(serverCtx))

		// Get ticket list
		publicTicketGroupRouter.GET("/list", publicTicket.GetUserTicketListHandler(serverCtx))
	}

	publicUserGroupRouter := router.Group("/v1/public/user")
	publicUserGroupRouter.Use(middleware.AuthMiddleware(serverCtx))

	{
		// Query User Affiliate Count
		publicUserGroupRouter.GET("/affiliate/count", publicUser.QueryUserAffiliateHandler(serverCtx))

		// Query User Affiliate List
		publicUserGroupRouter.GET("/affiliate/list", publicUser.QueryUserAffiliateListHandler(serverCtx))

		// Query User Balance Log
		publicUserGroupRouter.GET("/balance_log", publicUser.QueryUserBalanceLogHandler(serverCtx))

		// Update Bind Email
		publicUserGroupRouter.PUT("/bind_email", publicUser.UpdateBindEmailHandler(serverCtx))

		// Update Bind Mobile
		publicUserGroupRouter.PUT("/bind_mobile", publicUser.UpdateBindMobileHandler(serverCtx))

		// Bind OAuth
		publicUserGroupRouter.POST("/bind_oauth", publicUser.BindOAuthHandler(serverCtx))

		// Bind OAuth Callback
		publicUserGroupRouter.POST("/bind_oauth/callback", publicUser.BindOAuthCallbackHandler(serverCtx))

		// Bind Telegram
		publicUserGroupRouter.GET("/bind_telegram", publicUser.BindTelegramHandler(serverCtx))

		// Query User Commission Log
		publicUserGroupRouter.GET("/commission_log", publicUser.QueryUserCommissionLogHandler(serverCtx))

		// Query User Info
		publicUserGroupRouter.GET("/info", publicUser.QueryUserInfoHandler(serverCtx))

		// Get Login Log
		publicUserGroupRouter.GET("/login_log", publicUser.GetLoginLogHandler(serverCtx))

		// Update User Notify
		publicUserGroupRouter.PUT("/notify", publicUser.UpdateUserNotifyHandler(serverCtx))

		// Get OAuth Methods
		publicUserGroupRouter.GET("/oauth_methods", publicUser.GetOAuthMethodsHandler(serverCtx))

		// Update User Password
		publicUserGroupRouter.PUT("/password", publicUser.UpdateUserPasswordHandler(serverCtx))

		// Query User Subscribe
		publicUserGroupRouter.GET("/subscribe", publicUser.QueryUserSubscribeHandler(serverCtx))

		// Get Subscribe Log
		publicUserGroupRouter.GET("/subscribe_log", publicUser.GetSubscribeLogHandler(serverCtx))

		// Reset User Subscribe Token
		publicUserGroupRouter.PUT("/subscribe_token", publicUser.ResetUserSubscribeTokenHandler(serverCtx))

		// Unbind OAuth
		publicUserGroupRouter.POST("/unbind_oauth", publicUser.UnbindOAuthHandler(serverCtx))

		// Unbind Telegram
		publicUserGroupRouter.POST("/unbind_telegram", publicUser.UnbindTelegramHandler(serverCtx))

		// Unsubscribe
		publicUserGroupRouter.POST("/unsubscribe", publicUser.UnsubscribeHandler(serverCtx))

		// Pre Unsubscribe
		publicUserGroupRouter.POST("/unsubscribe/pre", publicUser.PreUnsubscribeHandler(serverCtx))

		// Verify Email
		publicUserGroupRouter.POST("/verify_email", publicUser.VerifyEmailHandler(serverCtx))
	}

	serverGroupRouter := router.Group("/v1/server")
	serverGroupRouter.Use(middleware.ServerMiddleware(serverCtx))

	{
		// Get server config
		serverGroupRouter.GET("/config", server.GetServerConfigHandler(serverCtx))

		// Push online users
		serverGroupRouter.POST("/online", server.PushOnlineUsersHandler(serverCtx))

		// Push user Traffic
		serverGroupRouter.POST("/push", server.ServerPushUserTrafficHandler(serverCtx))

		// Push server status
		serverGroupRouter.POST("/status", server.ServerPushStatusHandler(serverCtx))

		// Get user list
		serverGroupRouter.GET("/user", server.GetServerUserListHandler(serverCtx))
	}
}
